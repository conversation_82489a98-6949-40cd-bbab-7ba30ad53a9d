from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class NOAAClimateTool(Toolkit):
    """
    NOAA Climate Tool cho tìm kiếm dữ liệu khí hậu, thờ<PERSON> tiế<PERSON>, sự kiện cực đoan từ NOAA.
    """

    def __init__(self):
        super().__init__(
            name="NOAA Climate Data Search Tool",
            description="Tool cho tìm kiếm dữ liệu khí hậu, thờ<PERSON> tiết, sự kiện cực đoan từ NOAA.",
            tools=[self.search_noaa_climate]
        )

    async def search_noaa_climate(self, query: str, dataset: Optional[str] = None, location: Optional[str] = None, start_date: Optional[str] = None, end_date: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """
        Tì<PERSON> kiếm dữ liệu kh<PERSON> hậ<PERSON>, thờ<PERSON> tiế<PERSON>, sự kiện cực đoan từ NOAA.

        Parameters:
        - query: <PERSON><PERSON> kh<PERSON><PERSON> khí hậu/thời tiết/sự kiện (ví dụ: 'temperature anomaly', 'El Nino', 'hurricane', 'rainfall')
        - dataset: Mã/tên bộ dữ liệu NOAA (ví dụ: 'GHCND', 'GSOM', 'GSOY', 'NEXRAD2')
        - location: Địa điểm (tên thành phố, bang, quốc gia, hoặc mã vùng)
        - start_date: Ngày bắt đầu (YYYY-MM-DD)
        - end_date: Ngày kết thúc (YYYY-MM-DD)
        - limit: Số lượng kết quả tối đa (default: 10)

        Returns:
        - JSON với thông tin dữ liệu khí hậu, nguồn, mô tả, link NOAA
        """
        logger.info(f"Tìm kiếm NOAA Climate: query={query}, dataset={dataset}, location={location}, start={start_date}, end={end_date}")

        try:
            # NOAA API endpoint (token cần đăng ký, ở đây dùng demo token)
            api_url = "https://www.ncdc.noaa.gov/cdo-web/api/v2/data"
            token = "DEMO_TOKEN"  # Thay bằng token thật nếu có
            headers = {
                "token": token,
                "User-Agent": "NOAAClimateBot/1.0"
            }
            params = {
                "limit": limit
            }
            if dataset:
                params["datasetid"] = dataset
            if location:
                params["locationid"] = location
            if start_date:
                params["startdate"] = start_date
            if end_date:
                params["enddate"] = end_date
            if query:
                params["datatypeid"] = query  # Nếu query là mã datatype, ví dụ: 'TAVG', 'PRCP', 'SNOW'

            response = requests.get(api_url, headers=headers, params=params, timeout=15)
            if response.status_code == 401:
                return {
                    "status": "error",
                    "source": "NOAA",
                    "message": "API token không hợp lệ hoặc hết hạn. Đăng ký tại https://www.ncdc.noaa.gov/cdo-web/token",
                    "query": query
                }
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "NOAA",
                    "message": f"NOAA API trả về mã lỗi {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for item in data.get("results", []):
                results.append({
                    "date": item.get("date"),
                    "datatype": item.get("datatype"),
                    "station": item.get("station"),
                    "value": item.get("value"),
                    "attributes": item.get("attributes"),
                })

            return {
                "status": "success",
                "source": "NOAA",
                "query": query,
                "dataset": dataset,
                "location": location,
                "start_date": start_date,
                "end_date": end_date,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "<climate variable> region",
                    "<event> year",
                    "<temperature anomaly>",
                    "<rainfall> <city>",
                    "<hurricane> <year>"
                ],
                "official_data_url": "https://www.ncdc.noaa.gov/cdo-web/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm NOAA Climate: {str(e)}")
            return {
                "status": "error",
                "source": "NOAA",
                "message": str(e),
                "query": query
            }
