from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class NCBITaxonomyTool(Toolkit):
    """
    NCBI Taxonomy Tool for searching evolutionary taxonomy trees and metadata.
    """

    def __init__(self):
        super().__init__(
            name="NCBI Taxonomy Search Tool",
            description="Tool for searching evolutionary taxonomy trees and metadata from NCBI Taxonomy.",
            tools=[self.search_ncbi_taxonomy]
        )

    async def search_ncbi_taxonomy(self, query: str, taxid: Optional[int] = None) -> Dict[str, Any]:
        """
        Search NCBI Taxonomy for evolutionary tree and metadata.

        Parameters:
        - query: Scientific name or NCBI search string (e.g., 'Homo sapiens[ORGN]')
        - taxid: NCBI Taxonomy ID (if known, prioritized)

        Returns:
        - JSON with taxonomy lineage, scientific/common names, rank, and NCBI URLs
        """
        logger.info(f"Searching NCBI Taxonomy for: {query} (taxid={taxid})")

        try:
            # Step 1: Find taxid if not provided
            if not taxid:
                esearch_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi"
                esearch_params = {
                    "db": "taxonomy",
                    "term": query,
                    "retmode": "json"
                }
                esearch_resp = requests.get(esearch_url, params=esearch_params)
                if esearch_resp.status_code != 200:
                    return {
                        "status": "error",
                        "source": "NCBI Taxonomy",
                        "message": f"ESearch API returned status code {esearch_resp.status_code}",
                        "query": query
                    }
                esearch_data = esearch_resp.json()
                idlist = esearch_data.get("esearchresult", {}).get("idlist", [])
                if not idlist:
                    return {
                        "status": "error",
                        "source": "NCBI Taxonomy",
                        "message": "No taxid found for query",
                        "query": query
                    }
                taxid = idlist[0]

            # Step 2: Fetch taxonomy summary
            esummary_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi"
            esummary_params = {
                "db": "taxonomy",
                "id": taxid,
                "retmode": "json"
            }
            esummary_resp = requests.get(esummary_url, params=esummary_params)
            if esummary_resp.status_code != 200:
                return {
                    "status": "error",
                    "source": "NCBI Taxonomy",
                    "message": f"ESummary API returned status code {esummary_resp.status_code}",
                    "taxid": taxid
                }
            esummary_data = esummary_resp.json()
            docsum = esummary_data.get("result", {}).get(str(taxid), {})

            # Step 3: Extract lineage and details
            scientific_name = docsum.get("scientificname")
            common_name = docsum.get("commonname")
            rank = docsum.get("rank")
            lineage = docsum.get("lineage")
            division = docsum.get("division")
            genetic_code = docsum.get("geneticcode", {}).get("name")
            mitochondrial_genetic_code = docsum.get("mitochondrialgeneticcode", {}).get("name")
            other_names = docsum.get("othernames", [])
            parent_taxid = docsum.get("parenttaxid")
            ncbi_url = f"https://www.ncbi.nlm.nih.gov/Taxonomy/Browser/wwwtax.cgi?id={taxid}"

            result = {
                "taxid": taxid,
                "scientific_name": scientific_name,
                "common_name": common_name,
                "rank": rank,
                "lineage": lineage,
                "division": division,
                "genetic_code": genetic_code,
                "mitochondrial_genetic_code": mitochondrial_genetic_code,
                "other_names": other_names,
                "parent_taxid": parent_taxid,
                "ncbi_url": ncbi_url
            }

            return {
                "status": "success",
                "source": "NCBI Taxonomy",
                "query": query,
                "results": result
            }

        except Exception as e:
            log_debug(f"Error searching NCBI Taxonomy: {str(e)}")
            return {
                "status": "error",
                "source": "NCBI Taxonomy",
                "message": str(e),
                "query": query
            }
