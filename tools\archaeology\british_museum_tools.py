from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class BritishMuseumTool(Toolkit):
    """
    British Museum Tool for searching archaeological artifacts and collections.
    """

    def __init__(self):
        super().__init__(
            name="British Museum Search Tool",
            description="Tool for searching archaeological artifacts and collections from the British Museum.",
            tools=[self.search_british_museum]
        )

    async def search_british_museum(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """
        Search the British Museum's collection for archaeological artifacts.

        Parameters:
        - query: Search query using object type, culture, period, or registration number (e.g., 'statue,Greek,Classical', '1972,0121.1')
        - limit: Maximum number of results to return (default: 5)

        Returns:
        - JSON with search results including artifact metadata, images, and British Museum URLs
        """
        logger.info(f"Searching British Museum for: {query}")

        try:
            # British Museum Collection API endpoint
            # Documentation: https://www.britishmuseum.org/collection/about-collection-online/api
            api_url = "https://www.britishmuseum.org/api/collection/v1/search"
            params = {
                "q": query,
                "size": limit
            }
            response = requests.get(api_url, params=params)

            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "British Museum",
                    "message": f"British Museum API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            object_ids = data.get("objectIDs", [])[:limit]
            results = []

            # Fetch details for each object
            for obj_id in object_ids:
                detail_url = f"https://www.britishmuseum.org/api/collection/v1/objects/{obj_id}"
                detail_resp = requests.get(detail_url)
                if detail_resp.status_code != 200:
                    continue
                obj = detail_resp.json()
                # Extract main fields
                title = obj.get("title") or obj.get("objectType")
                description = obj.get("description")
                object_type = obj.get("objectType")
                culture = obj.get("culture")
                period = obj.get("period")
                production_date = obj.get("productionDate")
                materials = obj.get("materials")
                measurements = obj.get("measurements")
                registration_number = obj.get("registrationNumber")
                images = []
                for img in obj.get("images", []):
                    if img.get("url"):
                        images.append(img["url"])
                bm_url = f"https://www.britishmuseum.org/collection/object/{registration_number.replace(',', '-')}" if registration_number else None

                results.append({
                    "object_id": obj_id,
                    "title": title,
                    "description": description,
                    "object_type": object_type,
                    "culture": culture,
                    "period": period,
                    "production_date": production_date,
                    "materials": materials,
                    "measurements": measurements,
                    "registration_number": registration_number,
                    "images": images,
                    "british_museum_url": bm_url
                })

            return {
                "status": "success",
                "source": "British Museum",
                "query": query,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Error searching British Museum: {str(e)}")
            return {
                "status": "error",
                "source": "British Museum",
                "message": str(e),
                "query": query
            }
