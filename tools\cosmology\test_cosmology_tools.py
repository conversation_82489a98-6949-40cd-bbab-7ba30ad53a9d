#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho các cosmology tools đã đ<PERSON><PERSON><PERSON> c<PERSON>i tiến.
"""

import sys
import os
import json

# Thêm thư mục gốc vào Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_arxiv_tools():
    """Test ArXiv tools."""
    print("=== Testing ArXiv Tools ===")
    try:
        from tools.cosmology.arxiv_tools import ArXivTools
        
        tool = ArXivTools()
        
        # Test regular search
        print("--- Regular Search ---")
        result1 = tool.search_arxiv_papers("dark matter", max_results=2)
        print("Search result:", result1[:200] + "..." if len(result1) > 200 else result1)
        
        # Test recent papers
        print("\n--- Recent Papers ---")
        result2 = tool.get_recent_papers(3, 7, ["astro-ph.CO"])
        print("Recent papers result:", result2[:200] + "..." if len(result2) > 200 else result2)
        
        # Test trending topics
        print("\n--- Trending Topics ---")
        result3 = tool.get_trending_topics(3, "month")
        print("Trending topics result:", result3[:200] + "..." if len(result3) > 200 else result3)
        
        print("✅ ArXiv Tools: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing ArXiv Tools: {e}")
    print()

def test_cosmology_search_toolkit():
    """Test Cosmology Search Toolkit."""
    print("=== Testing Cosmology Search Toolkit ===")
    try:
        from tools.cosmology.cosmology_search_toolkit import CosmologySearchToolkit
        
        toolkit = CosmologySearchToolkit()
        
        # Test regular keyword generation
        print("--- arXiv Keywords ---")
        result1 = toolkit.generate_arxiv_keywords("dark matter", "astro-ph.CO", "simulation")
        print(result1)
        
        print("\n--- CERN Open Data Keywords ---")
        result2 = toolkit.generate_cern_opendata_keywords("CMS", "collision", "13 TeV")
        print(result2)
        
        print("\n--- INSPIRE-HEP Keywords ---")
        result3 = toolkit.generate_inspirehep_keywords("Higgs boson", "LHC", "Standard Model")
        print(result3)
        
        print("\n--- NASA ADS Keywords ---")
        result4 = toolkit.generate_nasa_ads_keywords("cosmic microwave background", "galaxy", "survey")
        print(result4)
        
        print("\n--- Wikipedia Physics Keywords ---")
        result5 = toolkit.generate_wikipedia_physics_keywords("general relativity", "cosmology")
        print(result5)
        
        # Test recent/trending keyword generation
        print("\n--- arXiv Recent Keywords ---")
        result6 = toolkit.generate_arxiv_recent_keywords("gr-qc", 30)
        print(result6)
        
        print("\n--- CERN Recent Keywords ---")
        result7 = toolkit.generate_cern_recent_keywords("ATLAS", 30)
        print(result7)
        
        print("\n--- INSPIRE-HEP Trending Keywords ---")
        result8 = toolkit.generate_inspirehep_trending_keywords("gravitational waves", "month")
        print(result8)
        
        print("\n--- NASA ADS Recent Keywords ---")
        result9 = toolkit.generate_nasa_ads_recent_keywords("ApJ", 30)
        print(result9)
        
        print("\n--- Wikipedia Physics Recent Keywords ---")
        result10 = toolkit.generate_wikipedia_physics_recent_keywords(30, "en")
        print(result10)
        
        print("✅ Cosmology Search Toolkit: SUCCESS")
        
    except Exception as e:
        print(f"❌ Error testing Search Toolkit: {e}")
    print()

def test_other_tools():
    """Test other cosmology tools briefly."""
    print("=== Testing Other Cosmology Tools ===")
    
    # Test CERN Open Data (if available)
    try:
        print("--- CERN Open Data Tools ---")
        from tools.cosmology.cern_opendata_tools import CERNOpenDataTools
        tool = CERNOpenDataTools()
        print("CERN Open Data tools loaded successfully")
    except Exception as e:
        print(f"CERN Open Data tools error: {e}")
    
    # Test INSPIRE-HEP (if available)
    try:
        print("--- INSPIRE-HEP Tools ---")
        from tools.cosmology.inspirehep_tools import INSPIREHEPTools
        tool = INSPIREHEPTools()
        print("INSPIRE-HEP tools loaded successfully")
    except Exception as e:
        print(f"INSPIRE-HEP tools error: {e}")
    
    # Test NASA ADS (if available)
    try:
        print("--- NASA ADS Tools ---")
        from tools.cosmology.nasa_ads_physics_tools import NASAADSPhysicsTools
        tool = NASAADSPhysicsTools()
        print("NASA ADS tools loaded successfully")
    except Exception as e:
        print(f"NASA ADS tools error: {e}")
    
    # Test Wikipedia Physics (if available)
    try:
        print("--- Wikipedia Physics Tools ---")
        from tools.cosmology.wikipedia_physics_tools import WikipediaPhysicsTools
        tool = WikipediaPhysicsTools()
        print("Wikipedia Physics tools loaded successfully")
    except Exception as e:
        print(f"Wikipedia Physics tools error: {e}")
    
    print()

def test_package_import():
    """Test package-level imports."""
    print("=== Testing Package Import ===")
    try:
        from tools.cosmology import (
            ArXivTools,
            CosmologySearchToolkit
        )
        print("✅ Core package imports successful")
        
        # Test instantiation
        tools = [
            ArXivTools(),
            CosmologySearchToolkit()
        ]
        print("✅ Core tool instantiation successful")
        
    except Exception as e:
        print(f"❌ Package import error: {e}")
    print()

def main():
    """Chạy tất cả các test."""
    print("Testing Cosmology Tools Functions")
    print("=" * 50)
    print()
    
    # Test các tool đã cải tiến
    test_arxiv_tools()
    test_cosmology_search_toolkit()
    test_other_tools()
    test_package_import()
    
    print("=" * 50)
    print("Testing completed!")
    print("\n📊 Summary:")
    print("✅ Working: ArXiv Tools (with recent/trending), Cosmology Search Toolkit")
    print("⏳ Need checking: CERN, INSPIRE-HEP, NASA ADS, Wikipedia Physics")

if __name__ == "__main__":
    main()
