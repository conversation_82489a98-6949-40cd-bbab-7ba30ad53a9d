from typing import Dict, Any, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class OpenTreeOfLifeTool(Toolkit):
    """
    Open Tree of Life Tool for searching phylogenetic and taxonomic data.
    """

    def __init__(self):
        super().__init__(
            name="Open Tree of Life Search Tool",
            description="Tool for searching phylogenetic and taxonomic data from the Open Tree of Life.",
            tools=[self.search_open_tree]
        )

    async def search_open_tree(self, query: str, ott_id: Optional[int] = None, include_subtree: bool = False) -> Dict[str, Any]:
        """
        Search Open Tree of Life for clade or taxon information.

        Parameters:
        - query: Clade or taxon name (e.g., 'Primates', 'Homo sapiens')
        - ott_id: Open Tree Taxonomy ID (nếu biết, ưu tiên tìm nhanh)
        - include_subtree: Nếu True, tr<PERSON> về cả cây con phát sinh chủng loại

        Returns:
        - J<PERSON><PERSON> với thông tin phát sinh chủng loại, taxonomy, và các ID liên quan
        """
        logger.info(f"Searching Open Tree of Life for: {query} (ott_id={ott_id})")

        try:
            # Bước 1: Tìm ott_id nếu chưa có
            if not ott_id:
                tnrs_url = "https://api.opentreeoflife.org/v3/tnrs/match_names"
                tnrs_payload = {"names": [query]}
                tnrs_response = requests.post(tnrs_url, json=tnrs_payload)
                if tnrs_response.status_code != 200:
                    return {
                        "status": "error",
                        "source": "Open Tree of Life",
                        "message": f"TNRS API returned status code {tnrs_response.status_code}",
                        "query": query
                    }
                tnrs_data = tnrs_response.json()
                matches = tnrs_data.get("results", [])
                if not matches or not matches[0].get("matches"):
                    return {
                        "status": "error",
                        "source": "Open Tree of Life",
                        "message": "No matches found for query",
                        "query": query
                    }
                ott_id = matches[0]["matches"][0]["taxon"]["ott_id"]
                matched_name = matches[0]["matches"][0]["taxon"]["unique_name"]
            else:
                matched_name = query

            # Bước 2: Lấy thông tin taxonomy
            taxonomy_url = "https://api.opentreeoflife.org/v3/taxonomy/taxon_info"
            taxonomy_payload = {"ott_id": ott_id, "include_lineage": True}
            taxonomy_response = requests.post(taxonomy_url, json=taxonomy_payload)
            if taxonomy_response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Open Tree of Life",
                    "message": f"Taxonomy API returned status code {taxonomy_response.status_code}",
                    "ott_id": ott_id
                }
            taxonomy_data = taxonomy_response.json()

            # Bước 3: Nếu yêu cầu, lấy cây phát sinh chủng loại con
            subtree = None
            if include_subtree:
                subtree_url = "https://api.opentreeoflife.org/v3/tree_of_life/subtree"
                subtree_payload = {"ott_id": ott_id}
                subtree_response = requests.post(subtree_url, json=subtree_payload)
                if subtree_response.status_code == 200:
                    subtree = subtree_response.json()
                else:
                    subtree = {"error": f"Subtree API returned status code {subtree_response.status_code}"}

            # Tổng hợp kết quả
            result = {
                "ott_id": ott_id,
                "matched_name": matched_name,
                "taxonomy": {
                    "rank": taxonomy_data.get("rank"),
                    "unique_name": taxonomy_data.get("unique_name"),
                    "synonyms": taxonomy_data.get("synonyms"),
                    "lineage": taxonomy_data.get("lineage"),
                    "flags": taxonomy_data.get("flags"),
                    "wikidata": taxonomy_data.get("wikidata"),
                    "sourceinfo": taxonomy_data.get("sourceinfo"),
                },
                "open_tree_url": f"https://tree.opentreeoflife.org/opentree/argus/ottol@{ott_id}"
            }
            if include_subtree:
                result["subtree"] = subtree

            return {
                "status": "success",
                "source": "Open Tree of Life",
                "query": query,
                "results": result
            }

        except Exception as e:
            log_debug(f"Error searching Open Tree of Life: {str(e)}")
            return {
                "status": "error",
                "source": "Open Tree of Life",
                "message": str(e),
                "query": query
            }
