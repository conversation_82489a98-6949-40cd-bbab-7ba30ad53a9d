from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class ProjectGutenbergLitTool(Toolkit):
    """
    Project Gutenberg Literature Tool cho tìm kiếm tác phẩm văn học, t<PERSON><PERSON> g<PERSON>, thể loại từ Project Gutenberg.
    """

    def __init__(self):
        super().__init__(
            name="Project Gutenberg Literature Search Tool",
            description="Tool cho tìm kiếm tác phẩm văn học, tác gi<PERSON>, thể lo<PERSON>, bản dịch từ Project Gutenberg.",
            tools=[self.search_gutenberg_lit]
        )

    async def search_gutenberg_lit(self, query: str, author: Optional[str] = None, topic: Optional[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm Project Gutenberg cho tác phẩm văn học, tá<PERSON> gi<PERSON>, thể loại.

        Parameters:
        - query: <PERSON><PERSON><PERSON> t<PERSON><PERSON>h<PERSON>m, chủ đề, từ khóa văn họ<PERSON> (ví dụ: 'Pride and Prejudice', 'Shakespeare', 'romantic poetry')
        - author: <PERSON><PERSON><PERSON> t<PERSON><PERSON> gi<PERSON> (ví dụ: '<PERSON> <PERSON>', '<PERSON>')
        - topic: Chủ đề hoặc th<PERSON> loại (ví dụ: 'poetry', 'novel', 'drama')
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với tiêu đề, tác giả, chủ đề, năm, mô tả, link Project Gutenberg
        """
        logger.info(f"Tìm kiếm Project Gutenberg Literature: query={query}, author={author}, topic={topic}")

        try:
            # Sử dụng API của Gutendex (Project Gutenberg open API)
            api_url = "https://gutendex.com/books"
            params = {
                "search": query,
                "languages": "en"
            }
            if author:
                params["author"] = author
            if topic:
                params["topic"] = topic

            response = requests.get(api_url, params=params)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Project Gutenberg",
                    "message": f"Gutenberg API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for book in data.get("results", [])[:limit]:
                download_url = None
                for fmt, url in book.get("formats", {}).items():
                    if "text/html" in fmt and "utf-8" in fmt:
                        download_url = url
                        break
                    if "text/plain" in fmt and not download_url:
                        download_url = url
                results.append({
                    "title": book.get("title"),
                    "authors": [a.get("name") for a in book.get("authors", [])],
                    "subjects": book.get("subjects"),
                    "bookshelves": book.get("bookshelves"),
                    "languages": book.get("languages"),
                    "copyright": book.get("copyright"),
                    "download_count": book.get("download_count"),
                    "gutenberg_id": book.get("id"),
                    "gutenberg_url": f"https://www.gutenberg.org/ebooks/{book.get('id')}",
                    "download_url": download_url
                })

            return {
                "status": "success",
                "source": "Project Gutenberg",
                "query": query,
                "author": author,
                "topic": topic,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "Pride and Prejudice",
                    "Shakespeare",
                    "romantic poetry",
                    "Charles Dickens",
                    "Victorian novel",
                    "classic literature",
                    "poetry",
                    "drama",
                    "short story"
                ],
                "official_data_url": "https://www.gutenberg.org/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Project Gutenberg Literature: {str(e)}")
            return {
                "status": "error",
                "source": "Project Gutenberg",
                "message": str(e),
                "query": query
            }
