from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class InternetArchiveLitTool(Toolkit):
    """
    Internet Archive Literature Tool cho tìm kiếm sách, phê bình, tài liệu văn học từ Internet Archive.
    """

    def __init__(self):
        super().__init__(
            name="Internet Archive Literature Search Tool",
            description="Tool cho tìm kiếm sách, phê bình, tài liệu văn học từ Internet Archive.",
            tools=[self.search_internet_archive_lit]
        )

    async def search_internet_archive_lit(self, query: str, author: Optional[str] = None, year: Optional[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm Internet Archive cho sách, phê bình, tài liệu văn học.

        Parameters:
        - query: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>h<PERSON>, chủ đề, thể lo<PERSON>, hoặc từ khóa (ví dụ: 'Pride and Prejudice', 'literary criticism', 'modernism')
        - author: <PERSON><PERSON><PERSON><PERSON><PERSON> (ví dụ: 'Shakespeare', 'Virginia Woolf')
        - year: Năm xuất bản hoặc khoảng năm (ví dụ: '1922', '1900-1950')
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với tiêu đề, tác giả, năm, mô tả, chủ đề, link Internet Archive
        """
        logger.info(f"Tìm kiếm Internet Archive Literature: query={query}, author={author}, year={year}, limit={limit}")

        try:
            search_url = "https://archive.org/advancedsearch.php"
            # Xây dựng chuỗi truy vấn
            q = f'({query}) AND (subject:literature OR subject:"literary criticism" OR subject:poetry OR subject:novel OR subject:fiction)'
            if author:
                q += f' AND creator:"{author}"'
            if year:
                if '-' in year:
                    start, end = year.split('-')
                    q += f' AND (year:[{start} TO {end}])'
                else:
                    q += f' AND year:{year}'

            params = {
                "q": q,
                "fl[]": "identifier,title,creator,year,description,subject",
                "rows": limit,
                "output": "json"
            }
            response = requests.get(search_url, params=params, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Internet Archive",
                    "message": f"Archive search API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            docs = data.get("response", {}).get("docs", [])
            results = []
            for item in docs:
                identifier = item.get("identifier")
                archive_url = f"https://archive.org/details/{identifier}" if identifier else None
                results.append({
                    "identifier": identifier,
                    "title": item.get("title"),
                    "authors": item.get("creator"),
                    "year": item.get("year"),
                    "description": item.get("description"),
                    "subjects": item.get("subject"),
                    "archive_url": archive_url
                })

            return {
                "status": "success",
                "source": "Internet Archive",
                "query": query,
                "author": author,
                "year": year,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "Pride and Prejudice",
                    "literary criticism",
                    "modernism",
                    "Shakespeare",
                    "poetry anthology",
                    "Victorian novel",
                    "American literature",
                    "Virginia Woolf"
                ],
                "official_data_url": "https://archive.org/details/texts"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Internet Archive Literature: {str(e)}")
            return {
                "status": "error",
                "source": "Internet Archive",
                "message": str(e),
                "query": query
            }
