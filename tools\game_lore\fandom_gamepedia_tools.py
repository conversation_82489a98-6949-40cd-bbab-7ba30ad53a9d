from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class FandomGamepediaTool(Toolkit):
    """
    Fandom/Gamepedia Tool cho tìm kiếm nhân vật, faction, lore, vũ trụ game từ Fandom/Gamepedia.
    """

    def __init__(self):
        super().__init__(
            name="Fandom/Gamepedia Search Tool",
            tools=[self.search_fandom_gamepedia]
        )

    async def search_fandom_gamepedia(self, query: str, wiki: Optional[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm Fandom/Gamepedia cho nhân vật, faction, lore, vũ trụ game.

        Parameters:
        - query: Tên nhân vật, faction, sự kiện, lore, vũ trụ game (ví dụ: 'Elden Ring Ranni', 'Halo UNSC', 'Dark Souls boss')
        - wiki: Tên wiki cụ thể (ví dụ: 'eldenring', 'halo', 'darksouls') nếu muốn giới hạn tìm kiếm
        - limit: <PERSON><PERSON> lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với tiêu đề, mô tả, url, thumbnail, và wiki liên quan
        """
        logger.info(f"Tìm kiếm Fandom/Gamepedia: query={query}, wiki={wiki}")

        try:
            # Nếu chỉ định wiki, dùng API của wiki đó, ngược lại dùng API search toàn cầu
            if wiki:
                api_url = f"https://{wiki}.fandom.com/api.php"
            else:
                # Dùng API search toàn cầu của Fandom
                api_url = "https://www.fandom.com/api/v1/Search/List"

            params = {}
            if wiki:
                params = {
                    "action": "query",
                    "list": "search",
                    "srsearch": query,
                    "format": "json",
                    "srlimit": limit
                }
            else:
                params = {
                    "query": query,
                    "limit": limit
                }

            response = requests.get(api_url, params=params, timeout=10)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Fandom/Gamepedia",
                    "message": f"API returned status code {response.status_code}",
                    "query": query
                }

            results = []
            if wiki:
                data = response.json()
                for item in data.get("query", {}).get("search", []):
                    title = item.get("title")
                    snippet = item.get("snippet")
                    page_url = f"https://{wiki}.fandom.com/wiki/{title.replace(' ', '_')}"
                    results.append({
                        "title": title,
                        "snippet": snippet,
                        "wiki": wiki,
                        "url": page_url
                    })
            else:
                data = response.json()
                for item in data.get("items", []):
                    results.append({
                        "title": item.get("title"),
                        "snippet": item.get("snippet"),
                        "wiki": item.get("wiki", {}).get("name"),
                        "url": item.get("url"),
                        "thumbnail": item.get("thumbnail")
                    })

            return {
                "status": "success",
                "source": "Fandom/Gamepedia",
                "query": query,
                "wiki": wiki,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Fandom/Gamepedia: {str(e)}")
            return {
                "status": "error",
                "source": "Fandom/Gamepedia",
                "message": str(e),
                "query": query
            }
