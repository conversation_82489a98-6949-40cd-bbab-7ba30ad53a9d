import json
import time
import requests
from typing import Any, Dict, List, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger

class NasaAdsTools(Toolkit):
    def __init__(self, search_ads_papers: bool = True, token: Optional[str] = None,
                 timeout: int = 10, max_retries: int = 3, **kwargs):
        super().__init__(name="nasa_ads_tools", **kwargs)
        self.token = token or "T81buNnE5s6cdB8Fdl4SXzhurreMvnIgnAIostod"
        self.base_url = "https://api.adsabs.harvard.edu/v1/search/query"
        self.timeout = timeout
        self.max_retries = max_retries

        # Khởi tạo cache đơn giản
        self.cache = {}

        if search_ads_papers:
            self.register(self.search_nasa_ads_papers)

    def search_nasa_ads_papers(self, query: str, rows: int = 5) -> str:
        """
        Search NASA ADS papers using a query and return basic info.
        Args:
            query (str): The query string.
            rows (int): Number of papers to return (default: 5).
        Returns:
            str: JSON string of results.
        """
        log_debug(f"Searching NASA ADS for: {query}")

        # Ki<PERSON>m tra cache
        cache_key = f"{query}_{rows}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for: {query}")
            return self.cache[cache_key]

        headers = {
            "Authorization": f"Bearer {self.token}"
        }
        params = {
            "q": query,
            "fl": "title,author,abstract,pubdate,doi,bibcode",
            "rows": rows,
            "sort": "date desc"
        }

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"NASA ADS attempt {attempt+1}/{self.max_retries}")
                response = requests.get(
                    self.base_url,
                    headers=headers,
                    params=params,
                    timeout=self.timeout
                )
                response.raise_for_status()
                data = response.json()

                results = []
                for doc in data.get("response", {}).get("docs", []):
                    paper = {
                        "title": doc.get("title", [""])[0],
                        "authors": doc.get("author", [])[:3],  # Giới hạn số lượng tác giả
                        "abstract": self._truncate_text(doc.get("abstract", ""), 500),  # Giới hạn độ dài abstract
                        "published": doc.get("pubdate", ""),
                        "doi": doc.get("doi", [None])[0] if doc.get("doi") else None,
                        "bibcode": doc.get("bibcode", ""),
                        "ads_url": f"https://ui.adsabs.harvard.edu/abs/{doc.get('bibcode', '')}" if doc.get("bibcode") else None
                    }
                    results.append(paper)

                result_json = json.dumps(results, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"NASA ADS timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)  # Chờ 1 giây trước khi thử lại
            except requests.exceptions.RequestException as e:
                logger.warning(f"NASA ADS request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"NASA ADS unexpected error: {e}")
                break

        # Trả về kết quả trống nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to search NASA ADS failed for query: {query}")
        return json.dumps([])

    def _truncate_text(self, text: str, max_length: int = 500) -> str:
        """Giới hạn độ dài văn bản."""
        if not text or len(text) <= max_length:
            return text
        return text[:max_length] + "..."