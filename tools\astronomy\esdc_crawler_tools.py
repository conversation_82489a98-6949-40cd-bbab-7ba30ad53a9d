import json
import requests
from bs4 import BeautifulSoup
from pathlib import Path
from typing import Optional, List

from agno.tools import Toolkit
from agno.utils.log import log_debug, logger

class EsdcCrawlerTools(Toolkit):
    def __init__(self, search_esdc: bool = True, base_url: Optional[str] = None, **kwargs):
        super().__init__(name="esdc_crawler_tools", **kwargs)
        self.base_url = base_url or "https://www.cosmos.esa.int/web/esdc"
        if search_esdc:
            self.register(self.search_esdc_metadata)

    def search_esdc_metadata(self, keyword: str, max_items: int = 10) -> str:
        """
        Crawl ESDC page to find metadata related to the keyword.
        Args:
            keyword (str): Keyword to search in titles or summaries.
            max_items (int): Max number of metadata entries to return.
        Returns:
            str: JSON string of metadata list.
        """
        log_debug(f"Starting crawl at {self.base_url} for keyword: {keyword}")
        try:
            resp = requests.get(self.base_url)
            resp.raise_for_status()
            soup = BeautifulSoup(resp.text, "html.parser")

            # This example assumes items are in divs with class 'views-row' (common Drupal pattern)
            items = soup.select("div.views-row")
            results = []

            for item in items:
                # Extract title and link
                title_tag = item.select_one("a")
                title = title_tag.text.strip() if title_tag else None
                link = title_tag["href"] if title_tag and title_tag.has_attr("href") else None
                if link and not link.startswith("http"):
                    link = "https://www.cosmos.esa.int" + link

                # Extract summary or snippet (if any)
                summary_tag = item.select_one("div.field-content")
                summary = summary_tag.text.strip() if summary_tag else None

                # Filter by keyword presence (case insensitive)
                if title and keyword.lower() in title.lower() or (summary and keyword.lower() in summary.lower()):
                    results.append({
                        "title": title,
                        "summary": summary,
                        "link": link
                    })

                if len(results) >= max_items:
                    break

            log_debug(f"Found {len(results)} items matching '{keyword}'")
            return json.dumps(results, indent=4)
        except Exception as e:
            logger.error(f"Error crawling ESDC metadata: {e}")
            return json.dumps({"error": str(e)})
