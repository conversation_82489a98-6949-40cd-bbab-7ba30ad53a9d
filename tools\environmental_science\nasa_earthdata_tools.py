from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class NASAEarthDataTool(Toolkit):
    """
    NASA EarthData Tool cho tìm kiếm dữ liệu môi trư<PERSON>, v<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> hậu từ NASA EarthData.
    """

    def __init__(self):
        super().__init__(
            name="NASA EarthData Search Tool",
            description="Tool cho tìm kiếm dữ liệu môi trườ<PERSON>, vi<PERSON><PERSON> thá<PERSON>, kh<PERSON> hậu từ NASA EarthData.",
            tools=[self.search_nasa_earthdata]
        )

    async def search_nasa_earthdata(
        self,
        query: str,
        temporal: Optional[str] = None,
        bounding_box: Optional[str] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """
        Tì<PERSON> kiếm NASA EarthData cho dữ liệu môi trườ<PERSON>, v<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> hậ<PERSON>.

        Parameters:
        - query: <PERSON><PERSON> kh<PERSON>a tìm kiếm (ví dụ: 'land surface temperature', 'MODIS', 'precipitation', 'forest cover')
        - temporal: <PERSON><PERSON><PERSON><PERSON> thời gian (ví dụ: '2020-01-01T00:00:00Z,2020-12-31T23:59:59Z')
        - bounding_box: Hộp giới hạn không gian (ví dụ: '-180,-90,180,90')
        - limit: Số lượng kết quả tối đa (default: 10)

        Returns:
        - JSON với thông tin dataset, mô tả, thời gian, không gian, link tải về
        """
        logger.info(f"Tìm kiếm NASA EarthData: query={query}, temporal={temporal}, bbox={bounding_box}, limit={limit}")

        try:
            # Sử dụng CMR API (Common Metadata Repository) của NASA EarthData
            cmr_url = "https://cmr.earthdata.nasa.gov/search/collections.json"
            params = {
                "q": query,
                "page_size": limit
            }
            if temporal:
                params["temporal"] = temporal
            if bounding_box:
                params["bounding_box"] = bounding_box

            response = requests.get(cmr_url, params=params, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "NASA EarthData",
                    "message": f"CMR API trả về mã lỗi {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for item in data.get("feed", {}).get("entry", []):
                dataset_id = item.get("id")
                title = item.get("title")
                summary = item.get("summary")
                time_start = item.get("time_start")
                time_end = item.get("time_end")
                bbox = item.get("boxes")
                links = [l.get("href") for l in item.get("links", []) if l.get("rel") == "http://esipfed.org/ns/fedsearch/1.1/data#"]
                granule_url = f"https://search.earthdata.nasa.gov/search/granules?p={dataset_id}" if dataset_id else None
                results.append({
                    "dataset_id": dataset_id,
                    "title": title,
                    "summary": summary,
                    "time_start": time_start,
                    "time_end": time_end,
                    "bounding_box": bbox,
                    "download_links": links,
                    "granule_search_url": granule_url
                })

            return {
                "status": "success",
                "source": "NASA EarthData",
                "query": query,
                "temporal": temporal,
                "bounding_box": bounding_box,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "land surface temperature",
                    "MODIS NDVI",
                    "precipitation",
                    "forest cover",
                    "sea surface temperature",
                    "aerosol optical depth",
                    "carbon emissions",
                    "remote sensing",
                    "climate change"
                ],
                "official_data_url": "https://search.earthdata.nasa.gov/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm NASA EarthData: {str(e)}")
            return {
                "status": "error",
                "source": "NASA EarthData",
                "message": str(e),
                "query": query
            }
