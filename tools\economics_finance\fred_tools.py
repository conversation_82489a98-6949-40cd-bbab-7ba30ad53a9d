from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class FREDTool(Toolkit):
    """
    FRED Tool cho tìm kiếm dữ liệu kinh tế, tà<PERSON>, chỉ số vĩ mô từ FRED (Federal Reserve Economic Data - St. Louis Fed).
    """

    def __init__(self):
        super().__init__(
            name="FRED Economic Data Search Tool",
            description="Tool cho tìm kiếm dữ liệu kinh tế, tài ch<PERSON>h, chỉ số vĩ mô từ FRED (St. Louis Fed).",
            tools=[self.search_fred]
        )
        self.api_key = "YOUR_FRED_API_KEY"  # Thay bằng API key thật nếu có

    async def search_fred(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm FRED cho dữ liệu kinh tế, t<PERSON><PERSON>, chỉ số vĩ mô.

        Parameters:
        - query: Từ khóa về chỉ số, chủ đề, mã series (ví dụ: 'GDP', 'unemployment rate', 'CPI', 'interest rate', 'M2')
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với thông tin series, mô tả, đơn vị, tần suất, link FRED
        """
        logger.info(f"Tìm kiếm FRED: query={query}, limit={limit}")

        try:
            # Bước 1: Search series
            search_url = "https://api.stlouisfed.org/fred/series/search"
            params = {
                "search_text": query,
                "api_key": self.api_key,
                "file_type": "json",
                "limit": limit
            }
            response = requests.get(search_url, params=params, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "FRED",
                    "message": f"FRED API trả về mã lỗi {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for item in data.get("seriess", []):
                series_id = item.get("id")
                title = item.get("title")
                frequency = item.get("frequency")
                units = item.get("units")
                observation_start = item.get("observation_start")
                observation_end = item.get("observation_end")
                notes = item.get("notes")
                fred_url = f"https://fred.stlouisfed.org/series/{series_id}" if series_id else None

                results.append({
                    "series_id": series_id,
                    "title": title,
                    "frequency": frequency,
                    "units": units,
                    "observation_start": observation_start,
                    "observation_end": observation_end,
                    "notes": notes,
                    "fred_url": fred_url
                })

            return {
                "status": "success",
                "source": "FRED",
                "query": query,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "GDP",
                    "unemployment rate",
                    "CPI",
                    "interest rate",
                    "M2",
                    "federal funds rate",
                    "housing starts",
                    "consumer sentiment",
                    "exchange rate",
                    "stock market index"
                ],
                "official_data_url": "https://fred.stlouisfed.org/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm FRED: {str(e)}")
            return {
                "status": "error",
                "source": "FRED",
                "message": str(e),
                "query": query
            }
