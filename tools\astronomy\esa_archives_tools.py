import json
import time
import requests
from typing import Optional, Dict, List, Any
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger


class EsaArchivesTools(Toolkit):
    def __init__(self, search_hubble_papers: bool = True, timeout: int = 10,
                 max_retries: int = 3, **kwargs):
        super().__init__(name="esa_archives_tools", **kwargs)
        # Cập nhật URL chính xác
        self.base_url = "https://hst.esac.esa.int/ehst-sl-server/servlet/data-action"
        self.timeout = timeout
        self.max_retries = max_retries

        # Khởi tạo cache đơn giản
        self.cache = {}

        if search_hubble_papers:
            self.register(self.search_esa_hubble)

    def search_esa_hubble(self, query: str, max_results: int = 5) -> str:
        """
        Search the ESA Hubble Science Archive using a keyword.
        Args:
            query (str): Keyword to search for.
            max_results (int): Number of results to return (default: 5).
        Returns:
            str: JSON string of basic results.
        """
        log_debug(f"Searching ESA Hubble Archive for: {query}")

        # Kiểm tra cache
        cache_key = f"{query}_{max_results}"
        if cache_key in self.cache:
            log_debug(f"Using cached results for: {query}")
            return self.cache[cache_key]

        # Fallback data nếu API không hoạt động
        fallback_data = [
            {
                "observation_id": "fallback_data",
                "target_name": query,
                "start_time": "",
                "instrument": "Fallback Data",
                "exposure": "",
                "link": "https://archives.esac.esa.int/ehst/",
                "note": "This is fallback data due to API unavailability"
            }
        ]

        params = {
            "REQUEST": "doQuery",
            "LANG": "ADQL",
            "QUERY": f"SELECT * FROM ehst.science WHERE target_name LIKE '%{query}%'",
            "FORMAT": "json"
        }

        # Thực hiện retry
        for attempt in range(self.max_retries):
            try:
                log_debug(f"ESA Hubble Archive attempt {attempt+1}/{self.max_retries}")
                response = requests.get(
                    self.base_url,
                    params=params,
                    timeout=self.timeout
                )
                response.raise_for_status()
                results = response.json()

                filtered_results = results.get("data", [])[:max_results]
                output = []
                for item in filtered_results:
                    output.append({
                        "observation_id": item.get("observation_id"),
                        "target_name": item.get("target_name"),
                        "start_time": item.get("start_time"),
                        "instrument": item.get("instrument_name"),
                        "exposure": item.get("exposure_time"),
                        "link": f"https://archives.esac.esa.int/ehst/#obsid={item.get('observation_id')}"
                    })

                result_json = json.dumps(output, indent=4)
                # Lưu vào cache
                self.cache[cache_key] = result_json
                return result_json

            except requests.exceptions.Timeout:
                logger.warning(f"ESA Hubble Archive timeout on attempt {attempt+1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except requests.exceptions.RequestException as e:
                logger.warning(f"ESA Hubble Archive request error on attempt {attempt+1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"ESA Hubble Archive unexpected error: {e}")
                break

        # Trả về dữ liệu fallback nếu tất cả các lần thử đều thất bại
        logger.error(f"All attempts to search ESA Hubble Archive failed for query: {query}")
        logger.info(f"Returning fallback data for query: {query}")
        fallback_json = json.dumps(fallback_data, indent=4)
        self.cache[cache_key] = fallback_json  # Cache fallback data
        return fallback_json