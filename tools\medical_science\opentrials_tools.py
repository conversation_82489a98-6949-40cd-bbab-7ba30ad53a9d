from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class OpenTrialsTool(Toolkit):
    """
    OpenTrials Tool cho tìm kiếm clinical trial, kết quả thử nghiệm lâm sàng từ OpenTrials.
    """

    def __init__(self):
        super().__init__(
            name="OpenTrials Search Tool",
            description="Tool cho tìm kiếm clinical trial, kết quả thử nghiệm lâm sàng, dữ liệu nghiên cứu từ OpenTrials.",
            tools=[self.search_opentrials]
        )

    async def search_opentrials(self, query: str, status: Optional[str] = None, condition: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """
        Tìm kiếm OpenTrials cho clinical trial, kết quả thử nghiệm lâm sàng.

        Parameters:
        - query: Từ khóa tìm kiếm (ví dụ: 'COVID-19 vaccine', 'diabetes', 'remdesivir')
        - status: Trạng thái thử nghiệm (ví dụ: 'completed', 'recruiting', 'terminated')
        - condition: Bệnh/lĩnh vực nghiên cứu (ví dụ: 'cancer', 'hypertension')
        - limit: Số lượng kết quả tối đa (default: 10)

        Returns:
        - JSON với thông tin trial, trạng thái, sponsor, thời gian, link OpenTrials
        """
        logger.info(f"Tìm kiếm OpenTrials: query={query}, status={status}, condition={condition}")

        try:
            # OpenTrials API endpoint (dựa trên OpenTrials.net hoặc ClinicalTrialsAPI)
            api_url = "https://api.opentrials.net/v1/trials"
            params = {
                "q": query,
                "per_page": limit
            }
            if status:
                params["status"] = status
            if condition:
                params["condition"] = condition

            response = requests.get(api_url, params=params, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "OpenTrials",
                    "message": f"OpenTrials API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for trial in data.get("results", [])[:limit]:
                results.append({
                    "trial_id": trial.get("id"),
                    "public_title": trial.get("public_title"),
                    "scientific_title": trial.get("scientific_title"),
                    "status": trial.get("status"),
                    "condition": trial.get("condition"),
                    "interventions": trial.get("interventions"),
                    "sponsor": trial.get("sponsor"),
                    "start_date": trial.get("start_date"),
                    "end_date": trial.get("end_date"),
                    "url": trial.get("url") or f"https://opentrials.net/trials/{trial.get('id')}"
                })

            return {
                "status": "success",
                "source": "OpenTrials",
                "query": query,
                "status_filter": status,
                "condition_filter": condition,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm OpenTrials: {str(e)}")
            return {
                "status": "error",
                "source": "OpenTrials",
                "message": str(e),
                "query": query
            }
