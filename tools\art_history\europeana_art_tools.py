from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class EuropeanaArtTool(Toolkit):
    """
    Europeana Art Tool cho tìm kiếm tác phẩm, bộ sưu tập nghệ thuật, nghệ sĩ từ Europeana.
    """

    def __init__(self):
        super().__init__(
            name="Europeana Art Search Tool",
            description="Tool cho tìm kiếm tác phẩm, bộ sưu tập nghệ thuật, nghệ sĩ từ Europeana.",
            tools=[self.search_europeana_art]
        )
        self.api_key = "apidemo"  # Europeana cung cấp key demo, nên thay bằng key thật nếu có

    async def search_europeana_art(self, query: str, type_: Optional[str] = None, creator: Optional[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm <PERSON>a cho tác phẩm, bộ sư<PERSON> tậ<PERSON> nghệ thuậ<PERSON>, ng<PERSON><PERSON> sĩ.

        Parameters:
        - query: <PERSON>ừ kh<PERSON>a tên <PERSON><PERSON>, ngh<PERSON>, ch<PERSON> (vd: '<PERSON> Gogh', 'Impressionism', 'Mona <PERSON>')
        - type_: Loại hình nghệ thuật (vd: 'IMAGE', 'TEXT', 'SOUND', 'VIDEO', '3D')
        - creator: Tên nghệ sĩ (vd: 'Rembrandt', '<PERSON> da Vinci')
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với tiêu đề, nghệ sĩ, năm, mô tả, hình ảnh, link Europeana
        """
        logger.info(f"Tìm kiếm Europeana Art: query={query}, type={type_}, creator={creator}, limit={limit}")

        try:
            search_url = "https://www.europeana.eu/api/v2/search.json"
            params = {
                "wskey": self.api_key,
                "query": query,
                "rows": limit,
                "profile": "rich"
            }
            if type_:
                params["qf"] = f"TYPE:{type_}"
            if creator:
                params["qf"] = params.get("qf", "") + f" AND PROVIDER_AGGREGATION_EDM_CREATOR:{creator}"

            response = requests.get(search_url, params=params, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Europeana",
                    "message": f"Europeana API trả về mã lỗi {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for item in data.get("items", []):
                title = item.get("title", [None])[0]
                creators = item.get("dcCreator", [])
                year = item.get("year", [None])[0] if "year" in item else None
                description = item.get("dcDescription", [None])[0] if "dcDescription" in item else None
                image = item.get("edmPreview", [None])[0] if "edmPreview" in item else None
                europeana_url = item.get("link")
                data_provider = item.get("dataProvider", [None])[0] if "dataProvider" in item else None
                type_val = item.get("type")
                results.append({
                    "title": title,
                    "creators": creators,
                    "year": year,
                    "description": description,
                    "image": image,
                    "type": type_val,
                    "data_provider": data_provider,
                    "europeana_url": europeana_url
                })

            return {
                "status": "success",
                "source": "Europeana",
                "query": query,
                "type": type_,
                "creator": creator,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "Van Gogh",
                    "Impressionism",
                    "Mona Lisa",
                    "Rembrandt",
                    "Leonardo da Vinci",
                    "sculpture",
                    "Renaissance art",
                    "Dutch Golden Age",
                    "modern art"
                ],
                "official_data_url": "https://www.europeana.eu/en/collections/topic/14-art"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Europeana Art: {str(e)}")
            return {
                "status": "error",
                "source": "Europeana",
                "message": str(e),
                "query": query
            }
