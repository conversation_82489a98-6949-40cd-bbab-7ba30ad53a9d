# -*- coding: utf-8 -*-
from typing import List, Dict, Any
import json
from agno.tools import Toolkit
from agno.utils.log import logger

class AstronomySearchToolkits(Toolkit):
    """A custom Toolkit for generating search keywords for astronomy-related databases.

    This toolkit provides functions to generate search keywords for NASA ADS, SIMBAD,
    ESA Archives, ESDC, and Wikipedia, tailored for astronomical research.
    """

    # == Detailed Instructions for the Agent ==
    instruction = [
        "Bạn là một trợ lý nghiên cứu thiên văn học, chuyên cung cấp từ khóa tìm kiếm tối ưu cho các cơ sở dữ liệu thiên văn.",
        "<PERSON>hi sử dụng các công cụ trong AstronomySearchToolkit, tuân thủ các định dạng từ khóa được chỉ định như sau:",
        "- NASA ADS: Sử dụng định dạng '<object> <phenomenon>' hoặc 'author:<LastName>' (ví dụ: 'exoplanet atmosphere characterization', 'author:Hawking').",
        "- SIMBAD: Sử dụng định dạng '<object_identifier>' hoặc '<catalog_name> <object_number>' (ví dụ: 'M31', 'HD 209458').",
        "- ESA Archives: Sử dụng định dạng '<mission_name>/<instrument> <target>' (ví dụ: 'Gaia/RVS stellar spectra', 'Rosetta/OSIRIS 67P').",
        "- ESDC: Sử dụng định dạng '<mission_name> <target>' hoặc '<phenomenon>' (ví dụ: 'Gaia exoplanet', 'cosmic microwave background').",
        "- Wikipedia: Sử dụng định dạng '<general_concept>' hoặc '<specific_phenomenon>' (ví dụ: 'neutron star formation', 'Crab Nebula supernova').",
        "Kiểm tra tính hợp lệ của tham số đầu vào và trả về từ khóa phù hợp với từng cơ sở dữ liệu.",
        "Trả về kết quả dưới dạng JSON với trạng thái ('status'), danh sách từ khóa ('keywords'), và thông báo ('message').",
        "Nếu có lỗi, trả về trạng thái 'error' với mô tả lỗi chi tiết."
    ]

    # == Detailed Few-Shot Examples ==
    few_shot_examples = [
        {
            "user": "Tìm thông tin về hành tinh ngoài hệ mặt trời Kepler-186f.",
            "tool_calls": [
                {
                    "name": "generate_nasa_ads_keywords",
                    "arguments": {"topic": "Kepler-186f exoplanet discovery"}
                },
                {
                    "name": "generate_simbad_keywords",
                    "arguments": {"object_id": "Kepler-186f"}
                },
                {
                    "name": "generate_esa_keywords",
                    "arguments": {"mission_instrument": "Gaia/RVS", "target": "Kepler-186f"}
                },
                {
                    "name": "generate_esdc_keywords",
                    "arguments": {"topic": "Kepler-186f"}
                },
                {
                    "name": "generate_wikipedia_keywords",
                    "arguments": {"concept": "Kepler-186f exoplanet"}
                }
            ]
        },
        {
            "user": "Tìm nghiên cứu về hiện tượng bùng nổ tia gamma.",
            "tool_calls": [
                {
                    "name": "generate_nasa_ads_keywords",
                    "arguments": {"topic": "gamma-ray burst characteristics"}
                },
                {
                    "name": "generate_esdc_keywords",
                    "arguments": {"topic": "gamma-ray burst"}
                },
                {
                    "name": "generate_wikipedia_keywords",
                    "arguments": {"concept": "gamma-ray burst"}
                }
            ]
        },
        {
            "user": "Tìm thông tin về sao neutron từ dữ liệu của Gaia.",
            "tool_calls": [
                {
                    "name": "generate_esa_keywords",
                    "arguments": {"mission_instrument": "Gaia/RVS", "target": "neutron star"}
                },
                {
                    "name": "generate_wikipedia_keywords",
                    "arguments": {"concept": "neutron star formation"}
                }
            ]
        }
    ]

    def __init__(self):
        """Initializes the AstronomySearchToolkit."""
        super().__init__(
            name="astronomy_search_toolkit",
            tools=[
                self.generate_nasa_ads_keywords,
                self.generate_simbad_keywords,
                self.generate_esa_keywords,
                self.generate_esdc_keywords,
                self.generate_wikipedia_keywords
            ],
            instructions=self.instruction
        )
        self.few_shot_examples = self.few_shot_examples
        logger.info("AstronomySearchToolkit initialized.")

    def generate_nasa_ads_keywords(self, topic: str) -> str:
        """Generates search keywords for NASA ADS.

        Args:
            topic: The topic or phenomenon to search (e.g., 'exoplanet atmosphere characterization', 'author:Hawking').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating NASA ADS keywords for topic: '{topic}'")
        try:
            if not topic.strip():
                raise ValueError("Topic cannot be empty.")

            # Simulate keyword optimization for NASA ADS
            keywords = [topic]
            if "author:" not in topic:
                keywords.append(f"{topic} astronomy")
                keywords.append(f"{topic} research")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated NASA ADS keywords for topic '{topic}'."
            }
            logger.debug(f"NASA ADS keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating NASA ADS keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate NASA ADS keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_simbad_keywords(self, object_id: str) -> str:
        """Generates search keywords for SIMBAD.

        Args:
            object_id: The astronomical object identifier or catalog name (e.g., 'M31', 'HD 209458').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating SIMBAD keywords for object: '{object_id}'")
        try:
            if not object_id.strip():
                raise ValueError("Object ID cannot be empty.")

            # Simulate keyword optimization for SIMBAD
            keywords = [object_id]
            if object_id.startswith(("HD", "HIP", "NGC", "M")):
                keywords.append(f"object {object_id}")
            else:
                keywords.append(f"{object_id} properties")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated SIMBAD keywords for object '{object_id}'."
            }
            logger.debug(f"SIMBAD keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating SIMBAD keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate SIMBAD keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_esa_keywords(self, mission_instrument: str, target: str) -> str:
        """Generates search keywords for ESA Archives.

        Args:
            mission_instrument: The mission and instrument (e.g., 'Gaia/RVS', 'Rosetta/OSIRIS').
            target: The target of the search (e.g., 'stellar spectra', '67P').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating ESA keywords for mission/instrument: '{mission_instrument}', target: '{target}'")
        try:
            if not mission_instrument.strip() or not target.strip():
                raise ValueError("Mission/instrument and target cannot be empty.")

            # Simulate keyword optimization for ESA Archives
            keywords = [f"{mission_instrument} {target}"]
            keywords.append(f"{mission_instrument} {target} data")
            keywords.append(f"{mission_instrument} astronomy")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated ESA keywords for '{mission_instrument}' and target '{target}'."
            }
            logger.debug(f"ESA keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating ESA keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate ESA keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_esdc_keywords(self, topic: str) -> str:
        """Generates search keywords for ESDC (ESA Science Data Centre).

        Args:
            topic: The topic or target to search (e.g., 'Gaia exoplanet', 'cosmic microwave background').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating ESDC keywords for topic: '{topic}'")
        try:
            if not topic.strip():
                raise ValueError("Topic cannot be empty.")

            # Simulate keyword optimization for ESDC
            keywords = [topic]
            keywords.append(f"{topic} data")
            keywords.append(f"{topic} astronomy")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated ESDC keywords for topic '{topic}'."
            }
            logger.debug(f"ESDC keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating ESDC keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate ESDC keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

    def generate_wikipedia_keywords(self, concept: str) -> str:
        """Generates search keywords for Wikipedia.

        Args:
            concept: The general concept or phenomenon (e.g., 'neutron star formation', 'Crab Nebula supernova').

        Returns:
            A JSON string containing the status, generated keywords, and message.
        """
        logger.info(f"Generating Wikipedia keywords for concept: '{concept}'")
        try:
            if not concept.strip():
                raise ValueError("Concept cannot be empty.")

            # Simulate keyword optimization for Wikipedia
            keywords = [concept]
            keywords.append(f"{concept} astronomy")
            keywords.append(f"{concept} overview")

            result = {
                "status": "success",
                "keywords": keywords,
                "message": f"Generated Wikipedia keywords for concept '{concept}'."
            }
            logger.debug(f"Wikipedia keywords generated: {keywords}")
        except Exception as e:
            logger.error(f"Error generating Wikipedia keywords: {str(e)}", exc_info=True)
            result = {
                "status": "error",
                "message": f"Failed to generate Wikipedia keywords: {str(e)}"
            }

        return json.dumps(result, ensure_ascii=False, indent=4)

# == Example Usage (for testing) ==
if __name__ == "__main__":
    astro_toolkit = AstronomySearchToolkits()

    # Test case 1: Generate keywords for exoplanet research
    print("--- Test Case 1: Exoplanet Research ---")
    result1 = astro_toolkit.generate_nasa_ads_keywords("exoplanet atmosphere characterization")
    print(result1)

    # Test case 2: Generate SIMBAD keywords
    print("\n--- Test Case 2: SIMBAD Object ---")
    result2 = astro_toolkit.generate_simbad_keywords("M31")
    print(result2)

    # Test case 3: Generate ESA keywords
    print("\n--- Test Case 3: ESA Archives ---")
    result3 = astro_toolkit.generate_esa_keywords("Gaia/RVS", "stellar spectra")
    print(result3)

    # Test case 4: Generate ESDC keywords
    print("\n--- Test Case 4: ESDC ---")
    result4 = astro_toolkit.generate_esdc_keywords("cosmic microwave background")
    print(result4)

    # Test case 5: Generate Wikipedia keywords
    print("\n--- Test Case 5: Wikipedia ---")
    result5 = astro_toolkit.generate_wikipedia_keywords("neutron star formation")
    print(result5)

    # Print instructions and few-shot examples
    print("\n--- Instructions ---")
    for i, instr in enumerate(astro_toolkit.instructions):
        print(f"{i+1}. {instr}")

    print("\n--- Few-Shot Examples ---")
    for i, example in enumerate(astro_toolkit.few_shot_examples):
        print(f"Example {i+1}:")
        print(f"  User: {example['user']}")
        print(f"  Tool Calls: {json.dumps(example['tool_calls'], ensure_ascii=False, indent=2)}")