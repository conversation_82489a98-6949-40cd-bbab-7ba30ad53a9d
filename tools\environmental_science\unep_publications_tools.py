from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class UNEPPublicationsTool(Toolkit):
    """
    UNEP Publications Tool cho tìm kiếm tài liệu, b<PERSON><PERSON> c<PERSON>o, dữ liệu môi trường từ United Nations Environment Programme (UNEP).
    """

    def __init__(self):
        super().__init__(
            name="UNEP Publications Search Tool",
            description="Tool cho tìm kiếm tài liệu, báo c<PERSON>o, dữ liệu môi trường từ UNEP Publications.",
            tools=[self.search_unep_publications]
        )

    async def search_unep_publications(self, query: str, language: str = "en", limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm UNEP Publications cho tài liệu, báo cáo, dữ liệu môi trường.

        Parameters:
        - query: Từ khóa tìm kiếm (ví dụ: 'climate change', 'biodiversity', 'plastic pollution', 'ozone layer')
        - language: Mã ngôn ngữ (ví dụ: 'en', 'fr', 'es', 'ar', 'zh', 'ru')
        - limit: Số lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với tiêu đề, tác giả, năm, mô tả, chủ đề, link tài liệu UNEP
        """
        logger.info(f"Tìm kiếm UNEP Publications: query={query}, language={language}, limit={limit}")

        try:
            # UNEP Publications sử dụng OpenSearch API (hoặc endpoint REST)
            # Ví dụ: https://www.unep.org/resources/publications
            # Ở đây sử dụng endpoint tìm kiếm REST (giả lập)
            search_url = "https://www.unep.org/api/v1/search"
            params = {
                "q": query,
                "type": "publication",
                "lang": language,
                "limit": limit
            }
            response = requests.get(search_url, params=params, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "UNEP Publications",
                    "message": f"UNEP API trả về mã lỗi {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for item in data.get("results", []):
                title = item.get("title")
                authors = item.get("authors")
                year = item.get("year") or item.get("datePublished")
                description = item.get("description")
                subjects = item.get("subjects") or item.get("tags")
                unep_url = item.get("url") or item.get("link")
                results.append({
                    "title": title,
                    "authors": authors,
                    "year": year,
                    "description": description,
                    "subjects": subjects,
                    "unep_url": unep_url
                })

            return {
                "status": "success",
                "source": "UNEP Publications",
                "query": query,
                "language": language,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "climate change",
                    "biodiversity",
                    "plastic pollution",
                    "ozone layer",
                    "environmental policy",
                    "ecosystem restoration",
                    "sustainable development"
                ],
                "official_data_url": "https://www.unep.org/resources/publications"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm UNEP Publications: {str(e)}")
            return {
                "status": "error",
                "source": "UNEP Publications",
                "message": str(e),
                "query": query
            }
