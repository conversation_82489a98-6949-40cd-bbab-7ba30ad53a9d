from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class AO3FanficTool(Toolkit):
    """
    AO3 Fanfic Tool cho tìm kiếm fanfiction từ Archive of Our Own (AO3).
    """

    def __init__(self):
        super().__init__(
            name="AO3 Fanfic Search Tool",
            tools=[self.search_ao3_fanfic]
        )

    async def search_ao3_fanfic(self, query: str, tag: Optional[str] = None, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm AO3 cho fanfiction theo từ khóa, tag, pairing hoặc fandom.

        Parameters:
        - query: Tên nhân vật, pairing, fandom, hoặc từ khóa (ví dụ: 'Dragon Age Solas', 'Mass Effect Shepard', 'Harry Potter', 'Star Wars')
        - tag: Tag hoặc thể loại cụ thể (ví dụ: 'romance', 'hurt/comfort', 'AU')
        - limit: <PERSON><PERSON> lượng kết quả tối đa (default: 5)

        Returns:
        - J<PERSON><PERSON> với tiêu đề, tá<PERSON> g<PERSON>, fandom, tó<PERSON> tắt, tags, link AO3
        """
        logger.info(f"Tìm kiếm AO3: query={query}, tag={tag}")

        try:
            # AO3 không có public API, sử dụng endpoint tìm kiếm web (scraping nhẹ)
            base_url = "https://archiveofourown.org"
            search_url = f"{base_url}/works/search"
            params = {
                "work_search[query]": query,
                "work_search[sort_column]": "revised_at",
                "work_search[language_id]": "en",
                "commit": "Search"
            }
            if tag:
                params["work_search[other_tag_names]"] = tag

            headers = {
                "User-Agent": "Mozilla/5.0 (compatible; AO3FanficBot/1.0)"
            }
            response = requests.get(search_url, params=params, headers=headers, timeout=15)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "AO3",
                    "message": f"AO3 search returned status code {response.status_code}",
                    "query": query
                }

            # Đơn giản: lấy các link truyện đầu tiên trong kết quả (dùng regex)
            import re
            works = []
            for match in re.finditer(r'<li class="work blurb group" id="work_(\d+)">(.+?)</li>', response.text, re.DOTALL):
                if len(works) >= limit:
                    break
                work_id = match.group(1)
                work_html = match.group(2)

                # Tiêu đề
                title_match = re.search(r'<h4 class="heading">\s*<a href="([^"]+)"[^>]*>([^<]+)</a>', work_html)
                title = title_match.group(2).strip() if title_match else None
                work_url = base_url + title_match.group(1) if title_match else None

                # Tác giả
                author_match = re.search(r'rel="author">([^<]+)</a>', work_html)
                author = author_match.group(1).strip() if author_match else None

                # Fandom
                fandom_match = re.search(r'<h5 class="fandoms heading">(.+?)</h5>', work_html)
                fandom = re.sub(r'<.*?>', '', fandom_match.group(1)).strip() if fandom_match else None

                # Tóm tắt
                summary_match = re.search(r'<blockquote class="userstuff summary">(.+?)</blockquote>', work_html, re.DOTALL)
                summary = re.sub(r'<.*?>', '', summary_match.group(1)).strip() if summary_match else None

                # Tags
                tags = []
                for tag_match in re.finditer(r'<li class="[^"]*tags[^"]*"><a[^>]+>([^<]+)</a>', work_html):
                    tags.append(tag_match.group(1).strip())

                works.append({
                    "work_id": work_id,
                    "title": title,
                    "author": author,
                    "fandom": fandom,
                    "summary": summary,
                    "tags": tags,
                    "ao3_url": work_url
                })

            return {
                "status": "success",
                "source": "AO3",
                "query": query,
                "tag": tag,
                "results_count": len(works),
                "results": works
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm AO3: {str(e)}")
            return {
                "status": "error",
                "source": "AO3",
                "message": str(e),
                "query": query
            }
