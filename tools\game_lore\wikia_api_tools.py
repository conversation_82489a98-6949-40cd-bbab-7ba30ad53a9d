from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class WikiaApiTool(Toolkit):
    """
    Wikia API Tool cho tìm kiếm nội dung structured từ các fandom/game wikis sử dụng Wikia API.
    """

    def __init__(self):
        super().__init__(
            name="Wikia API Search Tool",
            tools=[self.search_wikia_api]
        )

    async def search_wikia_api(self, query: str, wiki: str, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm nội dung structured từ Wikia API.

        Parameters:
        - query: Từ khóa tìm kiếm (ví dụ: 'Dark Souls boss', 'StarCraft Zerg', 'Elden Ring Ranni')
        - wiki: Tên wiki (subdomain, ví dụ: 'elderscrolls', 'starcraft', 'darksouls', 'halo', 'marvel')
        - limit: <PERSON><PERSON> lư<PERSON>ng kết quả tối đa (default: 5)

        Returns:
        - JSO<PERSON> với tiêu đề, snippet, url, thumbnail, và các trường structured khác nếu có
        """
        logger.info(f"Tìm kiếm Wikia API: query={query}, wiki={wiki}, limit={limit}")

        try:
            # API endpoint cho MediaWiki (Wikia/Fandom)
            api_url = f"https://{wiki}.fandom.com/api.php"
            params = {
                "action": "query",
                "list": "search",
                "srsearch": query,
                "format": "json",
                "srlimit": limit
            }
            response = requests.get(api_url, params=params)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikia API",
                    "message": f"Wikia API trả về mã lỗi {response.status_code}",
                    "query": query,
                    "wiki": wiki
                }

            data = response.json()
            search_results = data.get("query", {}).get("search", [])
            results = []
            for item in search_results:
                title = item.get("title")
                snippet = item.get("snippet")
                pageid = item.get("pageid")
                url = f"https://{wiki}.fandom.com/wiki/{title.replace(' ', '_')}" if title else None

                # Lấy thumbnail nếu có (dùng API khác)
                thumbnail = None
                try:
                    image_api_url = f"https://{wiki}.fandom.com/api.php"
                    image_params = {
                        "action": "query",
                        "prop": "pageimages",
                        "titles": title,
                        "format": "json",
                        "pithumbsize": 200
                    }
                    img_resp = requests.get(image_api_url, params=image_params)
                    if img_resp.status_code == 200:
                        img_data = img_resp.json()
                        pages = img_data.get("query", {}).get("pages", {})
                        for page in pages.values():
                            if "thumbnail" in page:
                                thumbnail = page["thumbnail"].get("source")
                                break
                except Exception as img_err:
                    log_debug(f"Lỗi lấy thumbnail từ Wikia: {str(img_err)}")

                results.append({
                    "title": title,
                    "snippet": snippet,
                    "pageid": pageid,
                    "url": url,
                    "thumbnail": thumbnail
                })

            return {
                "status": "success",
                "source": "Wikia API",
                "query": query,
                "wiki": wiki,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm Wikia API: {str(e)}")
            return {
                "status": "error",
                "source": "Wikia API",
                "message": str(e),
                "query": query,
                "wiki": wiki
            }
