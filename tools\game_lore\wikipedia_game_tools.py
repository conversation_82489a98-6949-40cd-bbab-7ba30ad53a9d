from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class WikipediaGameTool(Toolkit):
    """
    Wikipedia Game Tool for searching games, series, characters, and fictional universes on Wikipedia.
    """

    def __init__(self):
        super().__init__(
            name="Wikipedia Game Lore Search Tool",
            description="Tool for searching games, series, characters, and fictional universes from Wikipedia.",
            tools=[self.search_wikipedia_game]
        )

    async def search_wikipedia_game(self, query: str, language: str = "en") -> Dict[str, Any]:
        """
        Search Wikipedia for games, series, characters, or fictional universes.

        Parameters:
        - query: Game title, series, character, or universe (e.g., 'Final Fantasy', 'Elden Ring Ranni', 'Halo UNSC')
        - language: Wikipedia language code (default: 'en')

        Returns:
        - JSON with summary, page URL, thumbnail, and related topics
        """
        logger.info(f"Searching Wikipedia ({language}) for: {query}")

        try:
            # Wikipedia API endpoint
            api_url = f"https://{language}.wikipedia.org/api/rest_v1/page/summary/{query.replace(' ', '_')}"
            response = requests.get(api_url)

            if response.status_code == 404:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": "No article found for query",
                    "query": query
                }
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Wikipedia",
                    "message": f"Wikipedia API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            summary = data.get("extract")
            page_url = data.get("content_urls", {}).get("desktop", {}).get("page")
            title = data.get("title")
            thumbnail = data.get("thumbnail", {}).get("source")

            # Optionally, get related topics (using search API)
            related = []
            try:
                search_url = f"https://{language}.wikipedia.org/w/api.php"
                # Tối ưu keyword: kết hợp game, series, character, universe, developer, list, lore
                search_params = {
                    "action": "query",
                    "list": "search",
                    "srsearch": f"{query} video game OR series OR character OR universe OR developer OR list OR lore",
                    "format": "json",
                    "srlimit": 5
                }
                search_resp = requests.get(search_url, params=search_params)
                if search_resp.status_code == 200:
                    search_data = search_resp.json()
                    for item in search_data.get("query", {}).get("search", []):
                        if item.get("title") != title:
                            related.append(item.get("title"))
            except Exception as rel_err:
                log_debug(f"Error fetching related Wikipedia topics: {str(rel_err)}")

            return {
                "status": "success",
                "source": "Wikipedia",
                "query": query,
                "title": title,
                "summary": summary,
                "page_url": page_url,
                "thumbnail": thumbnail,
                "related_topics": related
            }

        except Exception as e:
            log_debug(f"Error searching Wikipedia Game: {str(e)}")
            return {
                "status": "error",
                "source": "Wikipedia",
                "message": str(e),
                "query": query
            }
