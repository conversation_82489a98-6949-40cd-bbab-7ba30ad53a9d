from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class ArchnetTool(Toolkit):
    """
    Archnet Tool for searching open access archaeology data from Archnet.
    """

    def __init__(self):
        super().__init__(
            name="Archnet Search Tool",
            description="Tool for searching open access archaeology data, sites, and artifacts from Archnet.",
            tools=[self.search_archnet]
        )

    async def search_archnet(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """
        Search Archnet for archaeological sites, periods, cultures, or artifacts.

        Parameters:
        - query: Search string (e.g., 'Mesopotamia/Ur:Early Dynastic', 'Maya:ceramic')
        - limit: Maximum number of results to return (default: 5)

        Returns:
        - JSON with search results including title, description, type, period, culture, and Archnet URLs
        """
        logger.info(f"Searching Archnet for: {query}")

        try:
            # Archnet không có API chính thức, sử dụng endpoint t<PERSON><PERSON> kiếm web
            search_url = "https://archnet.org/api/v1/search"
            params = {
                "q": query,
                "per_page": limit
            }
            response = requests.get(search_url, params=params)

            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "Archnet",
                    "message": f"Archnet search API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            for item in data.get("results", []):
                archnet_url = f"https://archnet.org/{item.get('type', 'site')}s/{item.get('id')}" if item.get("id") else None
                results.append({
                    "id": item.get("id"),
                    "title": item.get("title"),
                    "type": item.get("type"),
                    "description": item.get("description"),
                    "period": item.get("period"),
                    "culture": item.get("culture"),
                    "archnet_url": archnet_url
                })

            return {
                "status": "success",
                "source": "Archnet",
                "query": query,
                "results_count": len(results),
                "results": results
            }

        except Exception as e:
            log_debug(f"Error searching Archnet: {str(e)}")
            return {
                "status": "error",
                "source": "Archnet",
                "message": str(e),
                "query": query
            }
