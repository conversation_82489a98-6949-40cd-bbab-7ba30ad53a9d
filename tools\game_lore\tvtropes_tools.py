from typing import Dict, Any, Optional
from agno.tools import Toolkit
from agno.utils.log import log_debug, logger
import requests

class TVTropesTool(Toolkit):
    """
    TV Tropes Tool cho tìm kiếm motif, trope, cốt truyện, nh<PERSON> vật, và các yếu tố lặp lại trong game, t<PERSON><PERSON><PERSON><PERSON>, phim từ TV Tropes.
    """

    def __init__(self):
        super().__init__(
            name="TV Tropes Search Tool",
            description="Tool cho tìm kiếm motif, trope, cốt truyện, nhân vật, và các yếu tố lặp lại trong game, tru<PERSON><PERSON><PERSON>, phim từ TV Tropes.",
            tools=[self.search_tvtropes]
        )

    async def search_tvtropes(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """
        Tìm kiếm TV Tropes cho motif, trope, cố<PERSON> tru<PERSON>, nhân vật, hoặc game.

        Parameters:
        - query: Tên trope, motif, game, hoặc chủ đề (ví dụ: 'Chosen One', 'JRPG', 'Dark Souls', 'time travel')
        - limit: <PERSON><PERSON> lượng kết quả tối đa (default: 5)

        Returns:
        - JSON với tiêu đề, mô tả, loại trope, và link TV Tropes
        """
        logger.info(f"Tìm kiếm TV Tropes: {query}")

        try:
            # TV Tropes không có API chính thức, sử dụng DuckDuckGo Instant Answer API để tìm link TV Tropes
            search_url = "https://api.duckduckgo.com/"
            params = {
                "q": f"site:tvtropes.org {query}",
                "format": "json",
                "no_redirect": 1,
                "no_html": 1
            }
            response = requests.get(search_url, params=params, timeout=10)
            if response.status_code != 200:
                return {
                    "status": "error",
                    "source": "TV Tropes",
                    "message": f"DuckDuckGo API returned status code {response.status_code}",
                    "query": query
                }

            data = response.json()
            results = []
            # Lấy các kết quả liên quan từ RelatedTopics
            related = data.get("RelatedTopics", [])
            count = 0
            for item in related:
                if count >= limit:
                    break
                # Một số item là nhóm, có thể lồng thêm 'Topics'
                if "Topics" in item:
                    for subitem in item["Topics"]:
                        if count >= limit:
                            break
                        if "FirstURL" in subitem and "Text" in subitem:
                            results.append({
                                "title": subitem.get("Text"),
                                "tvtropes_url": subitem.get("FirstURL"),
                                "snippet": subitem.get("Text")
                            })
                            count += 1
                else:
                    if "FirstURL" in item and "Text" in item:
                        results.append({
                            "title": item.get("Text"),
                            "tvtropes_url": item.get("FirstURL"),
                            "snippet": item.get("Text")
                        })
                        count += 1

            return {
                "status": "success",
                "source": "TV Tropes",
                "query": query,
                "results_count": len(results),
                "results": results,
                "keyword_guide": [
                    "Chosen One",
                    "JRPG",
                    "time travel",
                    "Dark Souls",
                    "plot twist",
                    "MacGuffin",
                    "Red Herring",
                    "game trope",
                    "character archetype"
                ],
                "official_data_url": "https://tvtropes.org/"
            }

        except Exception as e:
            log_debug(f"Lỗi khi tìm kiếm TV Tropes: {str(e)}")
            return {
                "status": "error",
                "source": "TV Tropes",
                "message": str(e),
                "query": query
            }
