"""
Công cụ Wikipedia tùy chỉnh sử dụng thư viện wikipedia trực tiếp.
"""

import json
import time
import logging
from typing import List, Dict, Any, Optional

from agno.tools import Toolkit
from agno.utils.log import log_debug, logger

class WikipediaAstronomyTools(Toolkit):
    """Công cụ Wikipedia tùy chỉnh sử dụng thư viện wikipedia trực tiếp."""
    
    def __init__(self, **kwargs):
        """
        Khởi tạo công cụ Wikipedia tùy chỉnh.
        
        Args:
            **kwargs: <PERSON><PERSON><PERSON> tham số khác
        """
        super().__init__(name="custom_wikipedia_tools", **kwargs)
        
        # Đăng ký các phương thức
        self.register(self.search_wikipedia_custom)
        self.register(self.search_wikipedia_with_language)
    
    def search_wikipedia_custom(self, query: str, max_results: int = 3) -> str:
        """
        Tì<PERSON> kiếm thông tin trên Wikipedia với số lượng kết quả tùy chỉnh.
        
        Args:
            query: Từ khóa tìm kiếm
            max_results: S<PERSON> lượng kết quả tối đa
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        log_debug(f"Searching Wikipedia for: {query} with max_results={max_results}")
        
        try:
            import wikipedia
            
            # Đo thời gian thực thi
            start_time = time.time()
            
            # Tìm kiếm
            search_results = wikipedia.search(query, results=max_results)
            
            # Lấy thông tin chi tiết
            results = []
            for title in search_results:
                try:
                    page = wikipedia.page(title)
                    results.append({
                        "title": page.title,
                        "summary": page.summary,
                        "url": page.url,
                        "categories": page.categories[:5] if hasattr(page, "categories") else []
                    })
                except Exception as e:
                    log_debug(f"Error retrieving page {title}: {e}")
            
            # Tính thời gian thực thi
            execution_time = time.time() - start_time
            
            # Thêm metadata
            metadata = {
                "query": query,
                "max_results": max_results,
                "execution_time": execution_time,
                "result_count": len(results)
            }
            
            # Tạo kết quả cuối cùng
            final_result = {
                "results": results,
                "metadata": metadata
            }
            
            return json.dumps(final_result, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error searching Wikipedia: {e}")
            return json.dumps({"error": str(e)})
    
    def search_wikipedia_with_language(self, query: str, language: str = "en", max_results: int = 3) -> str:
        """
        Tìm kiếm thông tin trên Wikipedia với ngôn ngữ tùy chỉnh.
        
        Args:
            query: Từ khóa tìm kiếm
            language: Ngôn ngữ tìm kiếm
            max_results: Số lượng kết quả tối đa
            
        Returns:
            Chuỗi JSON chứa kết quả tìm kiếm
        """
        log_debug(f"Searching Wikipedia for: {query} with language={language}, max_results={max_results}")
        
        try:
            import wikipedia
            
            # Đặt ngôn ngữ
            wikipedia.set_lang(language)
            
            # Đo thời gian thực thi
            start_time = time.time()
            
            # Tìm kiếm
            search_results = wikipedia.search(query, results=max_results)
            
            # Lấy thông tin chi tiết
            results = []
            for title in search_results:
                try:
                    page = wikipedia.page(title)
                    results.append({
                        "title": page.title,
                        "summary": page.summary,
                        "url": page.url,
                        "language": language
                    })
                except Exception as e:
                    log_debug(f"Error retrieving page {title}: {e}")
            
            # Tính thời gian thực thi
            execution_time = time.time() - start_time
            
            # Thêm metadata
            metadata = {
                "query": query,
                "language": language,
                "max_results": max_results,
                "execution_time": execution_time,
                "result_count": len(results)
            }
            
            # Tạo kết quả cuối cùng
            final_result = {
                "results": results,
                "metadata": metadata
            }
            
            return json.dumps(final_result, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error searching Wikipedia: {e}")
            return json.dumps({"error": str(e)})